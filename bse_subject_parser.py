#!/usr/bin/env python3
"""
BSE Subject Parser
Parses the subject column in BSE corporate announcements table to extract:
- Company Name
- BSE Code
- Classification
- Subject

Format: "Company Name - BSE Code - Classification - Subject"
Example: "ArisInfra Solutions Ltd - 544419 - Announcement under Regulation 30 (LODR)-Resignation of Chief Financial Officer (CFO)"
"""

import requests
import json
import re
from typing import Dict, Optional, Tuple


class BSESubjectParser:
    """Parser for BSE corporate announcements subject column"""

    def __init__(self, supabase_url: str, supabase_key: str):
        """Initialize with Supabase credentials"""
        self.supabase_url = supabase_url
        self.supabase_key = supabase_key
        self.table_name = "bse_corporate_announcements"

        # Setup headers for Supabase REST API
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }

    def parse_subject(self, subject: str) -> Dict[str, Optional[str]]:
        """
        Parse subject string to extract company name, BSE code, classification, and subject.

        Expected format: "Company Name - BSE Code - Classification - Subject"

        Args:
            subject: The subject string to parse

        Returns:
            Dictionary with parsed components
        """
        if not subject or not isinstance(subject, str):
            return {
                'company_name': None,
                'bse_code': None,
                'classification': None,
                'parsed_subject': None
            }

        # Split by ' - ' (space-dash-space)
        parts = subject.split(' - ')

        if len(parts) < 3:
            # If we can't parse properly, return None values
            return {
                'company_name': None,
                'bse_code': None,
                'classification': None,
                'parsed_subject': subject  # Keep original as parsed_subject
            }

        # Extract components
        company_name = parts[0].strip()
        bse_code = parts[1].strip()

        # For classification and subject, we need to handle cases where there might be
        # multiple dashes in the classification or subject parts
        if len(parts) == 3:
            # Format: "Company - Code - Classification-Subject" or "Company - Code - Classification"
            # Need to split the third part further
            remaining = parts[2]

            # Look for common classification patterns to split properly
            classification_patterns = [
                # Pattern 1: "Announcement under Regulation XX (LODR)-Subject"
                r'^(Announcement under Regulation \d+[^-]*)-(.+)$',
                # Pattern 2: "Compliances-Subject"
                r'^(Compliances)-(.+)$',
                # Pattern 3: Generic "Classification-Subject"
                r'^([^-]+?)-(.+)$'
            ]

            classification = None
            parsed_subject = None

            for pattern in classification_patterns:
                match = re.match(pattern, remaining)
                if match:
                    classification = match.group(1).strip()
                    parsed_subject = match.group(2).strip()
                    break

            # If no pattern matched, treat entire remaining as classification
            if classification is None:
                classification = remaining.strip()
                parsed_subject = None

        elif len(parts) >= 4:
            # Format: "Company - Code - Classification - Subject - ..."
            classification = parts[2].strip()
            # Join remaining parts as subject
            parsed_subject = ' - '.join(parts[3:]).strip()

        else:
            # Less than 3 parts - can't parse properly
            classification = None
            parsed_subject = None

        return {
            'company_name': company_name,
            'bse_code': bse_code,
            'classification': classification,
            'parsed_subject': parsed_subject
        }

    def add_columns_to_table(self) -> bool:
        """
        Add new columns to the BSE corporate announcements table.

        Returns:
            True if successful, False otherwise
        """
        try:
            print("📋 Adding new columns to BSE corporate announcements table...")

            # We'll use direct SQL execution via Supabase API
            # Note: This requires appropriate permissions
            alter_queries = [
                "ALTER TABLE bse_corporate_announcements ADD COLUMN IF NOT EXISTS bse_code TEXT;",
                "ALTER TABLE bse_corporate_announcements ADD COLUMN IF NOT EXISTS classification TEXT;",
                "ALTER TABLE bse_corporate_announcements ADD COLUMN IF NOT EXISTS parsed_subject TEXT;"
            ]

            # Try to execute each query
            for query in alter_queries:
                print(f"Executing: {query}")

                # Use the database query endpoint
                response = requests.post(
                    f"{self.supabase_url}/rest/v1/rpc/query",
                    headers=self.headers,
                    json={"query": query}
                )

                if response.status_code not in [200, 201]:
                    print(f"⚠️ Query may have failed (this is normal if columns already exist): {query}")
                    print(f"Response: {response.status_code} - {response.text}")
                    # Continue anyway - columns might already exist
                else:
                    print(f"✅ Query executed successfully")

            print("✅ Column addition process completed")
            return True

        except Exception as e:
            print(f"❌ Error adding columns: {e}")
            print("ℹ️ This might be normal if columns already exist or if we don't have DDL permissions")
            return True  # Continue anyway

    def get_records_batch(self, offset: int = 0, limit: int = 1000) -> list:
        """
        Get a batch of records from the table.

        Args:
            offset: Starting position
            limit: Number of records to fetch

        Returns:
            List of records
        """
        try:
            url = f"{self.supabase_url}/rest/v1/{self.table_name}"
            params = {
                'select': 'id,subject,bse_code,classification,parsed_subject',
                'offset': offset,
                'limit': limit,
                'order': 'id'
            }

            response = requests.get(url, headers=self.headers, params=params)

            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to fetch records: {response.status_code}")
                print(f"Response: {response.text}")
                return []

        except Exception as e:
            print(f"❌ Error fetching records: {e}")
            return []

    def update_record(self, record_id: str, parsed_data: Dict[str, Optional[str]]) -> bool:
        """
        Update a single record with parsed data.

        Args:
            record_id: The record ID to update
            parsed_data: Dictionary with parsed components

        Returns:
            True if successful, False otherwise
        """
        try:
            url = f"{self.supabase_url}/rest/v1/{self.table_name}"
            params = {'id': f'eq.{record_id}'}

            update_data = {
                'bse_code': parsed_data['bse_code'],
                'classification': parsed_data['classification'],
                'parsed_subject': parsed_data['parsed_subject']
            }

            response = requests.patch(url, headers=self.headers, params=params, json=update_data)

            return response.status_code in [200, 204]

        except Exception as e:
            print(f"❌ Error updating record {record_id}: {e}")
            return False

    def process_all_records(self) -> bool:
        """
        Process all records in the table to parse subject column.

        Returns:
            True if successful, False otherwise
        """
        try:
            print("🚀 Starting to process all records...")

            offset = 0
            batch_size = 1000
            total_processed = 0
            total_updated = 0

            while True:
                # Get batch of records
                records = self.get_records_batch(offset, batch_size)

                if not records:
                    break

                print(f"📦 Processing batch: {offset + 1} to {offset + len(records)}")

                batch_updated = 0
                for record in records:
                    # Skip if already processed (has bse_code)
                    if record.get('bse_code'):
                        continue

                    # Parse the subject
                    parsed_data = self.parse_subject(record.get('subject', ''))

                    # Update the record
                    if self.update_record(record['id'], parsed_data):
                        batch_updated += 1

                    total_processed += 1

                total_updated += batch_updated
                print(f"✅ Updated {batch_updated} records in this batch")

                # Move to next batch
                offset += len(records)

                # If we got fewer records than batch_size, we're done
                if len(records) < batch_size:
                    break

            print(f"🎉 Processing complete!")
            print(f"📊 Total records processed: {total_processed}")
            print(f"📊 Total records updated: {total_updated}")

            return True

        except Exception as e:
            print(f"❌ Error processing records: {e}")
            return False

    def test_parsing(self, test_subjects: list = None) -> None:
        """
        Test the parsing logic with sample subjects.

        Args:
            test_subjects: List of test subjects, uses defaults if None
        """
        if test_subjects is None:
            test_subjects = [
                "ArisInfra Solutions Ltd - 544419 - Announcement under Regulation 30 (LODR)-Resignation of Chief Financial Officer (CFO)",
                "Duke Offshore Ltd - 531471 - Compliances-Certificate under Reg. 74 (5) of SEBI (DP) Regulations, 2018",
                "Titagarh Rail Systems Ltd - 532966 - Announcement under Regulation 30 (LODR)-Issue of Securities",
                "UTI Asset Management Company Ltd - 543238 - Fixed Record Date For The Purpose Of Final Dividend",
                "AXIS Bank Ltd - 532215 - Announcement under Regulation 30 (LODR)-Analyst / Investor Meet - Intimation"
            ]

        print("🧪 Testing subject parsing...")
        print("=" * 80)

        for i, subject in enumerate(test_subjects, 1):
            print(f"\nTest {i}:")
            print(f"Original: {subject}")

            parsed = self.parse_subject(subject)
            print(f"Company Name: {parsed['company_name']}")
            print(f"BSE Code: {parsed['bse_code']}")
            print(f"Classification: {parsed['classification']}")
            print(f"Parsed Subject: {parsed['parsed_subject']}")
            print("-" * 40)


def main():
    """Main function to run the BSE subject parser"""

    # Supabase configuration
    SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
    SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

    # Initialize parser
    parser = BSESubjectParser(SUPABASE_URL, SUPABASE_KEY)

    # Test parsing first
    print("🧪 Testing parsing logic...")
    parser.test_parsing()

    # Ask user for confirmation
    response = input("\n❓ Do you want to proceed with updating the database? (y/N): ")
    if response.lower() != 'y':
        print("❌ Operation cancelled by user")
        return

    # Add columns to table
    print("\n📋 Adding new columns to table...")
    if not parser.add_columns_to_table():
        print("❌ Failed to add columns. Exiting.")
        return

    # Process all records
    print("\n🔄 Processing all records...")
    if parser.process_all_records():
        print("✅ All records processed successfully!")
    else:
        print("❌ Some errors occurred during processing")


if __name__ == "__main__":
    main()