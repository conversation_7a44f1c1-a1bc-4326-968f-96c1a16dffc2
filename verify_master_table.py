#!/usr/bin/env python3
"""
Verify Master Corporate Announcements Table
Check the actual data and statistics
"""

import requests
import json

# Supabase configuration
SUPABASE_URL = "https://yvuwseolouiqhoxsieop.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dXdzZW9sb3VpcWhveHNpZW9wIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI1OTI2ODcsImV4cCI6MjA2ODE2ODY4N30.W7m701xSGJ5eh8oc4turmb4zO9-nz1Pbzqz-DL9EEow"

headers = {
    'apikey': SUPABASE_KEY,
    'Authorization': f'Bearer {SUPABASE_KEY}',
    'Content-Type': 'application/json'
}

def get_all_master_records():
    """Get all records from master table"""
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            records = response.json()
            print(f"📊 Total records in master table: {len(records)}")
            
            # Analyze by source
            nse_count = sum(1 for r in records if r.get('source_exchange') == 'NSE')
            bse_count = sum(1 for r in records if r.get('source_exchange') == 'BSE')
            has_nse_data = sum(1 for r in records if r.get('has_nse_data') == True)
            has_bse_data = sum(1 for r in records if r.get('has_bse_data') == True)
            merged_records = sum(1 for r in records if r.get('has_nse_data') == True and r.get('has_bse_data') == True)
            
            print(f"📈 Statistics:")
            print(f"   Source NSE: {nse_count}")
            print(f"   Source BSE: {bse_count}")
            print(f"   Has NSE data: {has_nse_data}")
            print(f"   Has BSE data: {has_bse_data}")
            print(f"   Merged records: {merged_records}")
            
            # Show sample records
            print(f"\n📋 Sample records:")
            for i, record in enumerate(records[:5]):
                print(f"Record {i+1}:")
                print(f"   ID: {record.get('id')}")
                print(f"   PDF Hash: {record.get('pdf_hash')}")
                print(f"   Source: {record.get('source_exchange')}")
                print(f"   Company: {record.get('company_name')}")
                print(f"   NSE Symbol: {record.get('nse_symbol')}")
                print(f"   BSE Code: {record.get('bse_code')}")
                print(f"   Has NSE Data: {record.get('has_nse_data')}")
                print(f"   Has BSE Data: {record.get('has_bse_data')}")
                print(f"   Subject: {record.get('subject', '')[:100]}...")
                print()
            
            return records
        else:
            print(f"❌ Error fetching records: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_duplicate_pdf_hashes():
    """Check for duplicate pdf_hash values"""
    
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            params={'select': 'pdf_hash'},
            timeout=30
        )
        
        if response.status_code == 200:
            records = response.json()
            pdf_hashes = [r.get('pdf_hash') for r in records if r.get('pdf_hash')]
            
            # Check for duplicates
            seen = set()
            duplicates = set()
            
            for hash_val in pdf_hashes:
                if hash_val in seen:
                    duplicates.add(hash_val)
                else:
                    seen.add(hash_val)
            
            print(f"🔍 PDF Hash Analysis:")
            print(f"   Total PDF hashes: {len(pdf_hashes)}")
            print(f"   Unique PDF hashes: {len(seen)}")
            print(f"   Duplicate PDF hashes: {len(duplicates)}")
            
            if duplicates:
                print(f"   Duplicate hashes: {list(duplicates)[:5]}...")
            
            return duplicates
        else:
            print(f"❌ Error checking duplicates: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_source_table_counts():
    """Check counts in source tables"""
    
    print("📊 Source table statistics:")
    
    # NSE table
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/nse_corporate_announcements",
            headers=headers,
            params={
                'is_duplicate': 'eq.false',
                'duplicate_check_status': 'eq.completed',
                'pdf_hash': 'not.is.null',
                'select': 'count'
            },
            timeout=30
        )
        
        if response.status_code == 200:
            nse_count = len(response.json())
            print(f"   NSE eligible records: {nse_count}")
        else:
            print(f"   NSE count error: {response.status_code}")
            
    except Exception as e:
        print(f"   NSE error: {e}")
    
    # BSE table
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/bse_corporate_announcements",
            headers=headers,
            params={
                'is_duplicate': 'eq.false',
                'duplicate_check_status': 'eq.completed',
                'pdf_hash': 'not.is.null',
                'select': 'count'
            },
            timeout=30
        )
        
        if response.status_code == 200:
            bse_count = len(response.json())
            print(f"   BSE eligible records: {bse_count}")
        else:
            print(f"   BSE count error: {response.status_code}")
            
    except Exception as e:
        print(f"   BSE error: {e}")

def test_upsert_functionality():
    """Test the upsert functionality with a sample record"""
    
    print("🧪 Testing upsert functionality...")
    
    # Create a test record
    test_record = {
        'pdf_hash': 'test-upsert-hash-123',
        'source_exchange': 'NSE',
        'has_nse_data': True,
        'has_bse_data': False,
        'company_name': 'Test Upsert Company',
        'nse_symbol': 'TESTUPSERT',
        'subject': 'Test Upsert Subject'
    }
    
    # Insert first time
    try:
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            json=test_record,
            timeout=30
        )
        
        if response.status_code in [200, 201]:
            print("✅ First insert successful")
        else:
            print(f"❌ First insert failed: {response.status_code} - {response.text}")
            return
            
    except Exception as e:
        print(f"❌ First insert error: {e}")
        return
    
    # Try to insert again (should fail due to unique constraint)
    try:
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            json=test_record,
            timeout=30
        )
        
        if response.status_code in [200, 201]:
            print("⚠️ Second insert succeeded (unexpected)")
        else:
            print("✅ Second insert failed as expected (unique constraint working)")
            
    except Exception as e:
        print(f"✅ Second insert error as expected: {e}")
    
    # Test update
    update_data = {
        'bse_code': 'TEST123',
        'has_bse_data': True,
        'details': 'Updated details from BSE'
    }
    
    try:
        response = requests.patch(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            params={'pdf_hash': f'eq.{test_record["pdf_hash"]}'},
            json=update_data,
            timeout=30
        )
        
        if response.status_code in [200, 204]:
            print("✅ Update successful")
        else:
            print(f"❌ Update failed: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"❌ Update error: {e}")
    
    # Clean up test record
    try:
        response = requests.delete(
            f"{SUPABASE_URL}/rest/v1/master_corporate_announcements",
            headers=headers,
            params={'pdf_hash': f'eq.{test_record["pdf_hash"]}'},
            timeout=30
        )
        
        if response.status_code in [200, 204]:
            print("✅ Test record cleaned up")
        else:
            print(f"⚠️ Cleanup warning: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️ Cleanup error: {e}")

def main():
    """Main verification function"""
    
    print("🔍 Master Corporate Announcements Table Verification")
    print("=" * 60)
    
    # Check all records
    print("\n1️⃣ Getting all master records...")
    records = get_all_master_records()
    
    # Check for duplicates
    print("\n2️⃣ Checking for duplicate PDF hashes...")
    duplicates = check_duplicate_pdf_hashes()
    
    # Check source tables
    print("\n3️⃣ Checking source table counts...")
    check_source_table_counts()
    
    # Test upsert functionality
    print("\n4️⃣ Testing upsert functionality...")
    test_upsert_functionality()
    
    print("\n✅ Verification completed!")

if __name__ == "__main__":
    main()
